# AI控制Chrome浏览器自动化系统 - 完整部署教程

## 📋 项目简介

本项目将帮你搭建一个完整的AI控制Chrome浏览器自动化系统，实现：
- 🤖 用自然语言控制Chrome浏览器
- 📸 AI智能截图分析（支持视觉理解）
- 🔍 跨标签页语义搜索
- 📊 自动化数据收集和分析
- 🎨 页面自定义修改
- 📚 智能书签管理
- 🌐 网络请求监控

## 🎯 系统架构

```
AI模型 (Ollama) - 本地AI推理引擎
    ↓
Open WebUI - 现代化Web界面
    ↓ MCP Protocol
mcp-chrome服务器 (127.0.0.1:12306) - 桥接服务
    ↓ Native Messaging
Chrome扩展 - 浏览器接口
    ↓ Chrome APIs
Chrome浏览器 - 执行环境
```

## 💻 硬件要求

### 最低配置
- **内存**: 8GB RAM
- **CPU**: 4核心处理器
- **存储**: 15GB 可用空间
- **系统**: Windows 10/11, macOS 10.15+, Linux

### 推荐配置
- **内存**: 16GB+ RAM
- **CPU**: 8核心处理器或更好
- **存储**: 25GB+ 可用空间（SSD推荐）
- **显卡**: 可选，用于GPU加速推理

## 🚀 第一步：安装基础环境

### 1.1 安装Node.js

1. **下载Node.js LTS版本**
   - 访问：https://nodejs.org/
   - 下载LTS版本（推荐18.x或20.x）

2. **安装Node.js**
   ```bash
   # Windows (使用winget)
   winget install OpenJS.NodeJS.LTS

   # macOS (使用Homebrew)
   brew install node

   # Linux (Ubuntu/Debian)
   curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

3. **验证安装**
   ```bash
   node --version
   npm --version
   ```

### 1.2 安装pnpm（推荐）

```bash
# 全平台通用
npm install -g pnpm

# 或使用系统包管理器
# Windows
winget install pnpm.pnpm

# macOS
brew install pnpm
```

### 1.3 安装Python（用于Open WebUI）

```bash
# Windows
winget install Python.Python.3.11

# macOS
brew install python@3.11

# Linux
sudo apt update && sudo apt install python3.11 python3.11-pip
```

## 🔧 第二步：下载和配置mcp-chrome项目

### 2.1 下载项目

1. **从GitHub下载**
   - 访问：https://github.com/hangwin/mcp-chrome
   - 点击"Code" → "Download ZIP"
   - 解压到工作目录（如：`D:\mcp-chrome-master`）

### 2.2 准备本地模型文件

> 🎯 **重要**：本项目已优化为本地模式，无需在线下载模型，启动更快更稳定！

1. **创建模型目录**
   ```powershell
   # 在项目根目录创建模型文件夹
   mkdir xiazaide
   ```

2. **下载语义搜索模型**（可选，但推荐）
   - **multilingual-e5-small** (118MB) - 轻量级，推荐
   - **multilingual-e5-base** (279MB) - 更好效果

   **下载地址**：
   ```
   multilingual-e5-small:
   https://huggingface.co/Xenova/multilingual-e5-small/tree/main

   multilingual-e5-base:
   https://huggingface.co/Xenova/multilingual-e5-base/tree/main
   ```

3. **组织模型文件结构**
   ```
   xiazaide/
   ├── Xenova/
   │   ├── multilingual-e5-small/
   │   │   ├── config.json
   │   │   ├── model_quantized.onnx
   │   │   ├── special_tokens_map.json
   │   │   ├── tokenizer_config.json
   │   │   └── tokenizer.json
   │   └── multilingual-e5-base/
   │       ├── config.json
   │       ├── model_quantized.onnx
   │       ├── special_tokens_map.json
   │       ├── tokenizer_config.json
   │       └── tokenizer.json
   ```

### 2.3 一键配置本地模式

```powershell
# 进入项目目录
cd D:\mcp-chrome-master

# 运行本地模型配置脚本（自动安装依赖、配置模型、构建项目）
powershell -ExecutionPolicy Bypass -File setup-local-models.ps1
```

这个脚本会自动完成：
- ✅ 安装所有项目依赖
- ✅ 复制模型文件到正确位置
- ✅ 配置本地模式（禁用在线下载）
- ✅ 构建Chrome扩展
- ✅ 构建Native Server

### 2.4 手动构建（如果自动脚本失败）

```powershell
# 安装依赖
pnpm install

# 构建shared模块
pnpm --filter chrome-mcp-shared build

# 构建Chrome扩展
cd mcp-chrome-master/app/chrome-extension
pnpm run build

# 构建native-server
cd ../native-server
pnpm run build
```

## 🌐 第三步：安装Chrome扩展

### 3.1 使用构建好的扩展

> 如果您使用了 `setup-local-models.ps1` 脚本，扩展已经自动构建完成！

扩展位置：`mcp-chrome-master\app\chrome-extension\.output\chrome-mv3`

### 3.2 在Chrome中加载扩展

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `mcp-chrome-master\app\chrome-extension\.output\chrome-mv3` 文件夹

4. **验证安装**
   - 扩展列表中应显示"Chrome MCP Server"
   - Chrome工具栏出现扩展图标
   - 记录扩展ID（类似：`nflcmnkchaaogfjpcdnlgmleblhhmhdg`）

## 🔗 第四步：配置Native Messaging

### 4.1 更新扩展ID配置

> ⚠️ **重要**：需要使用您实际的扩展ID

```powershell
# 进入native-server目录
cd mcp-chrome-master/app/native-server

# 编辑扩展ID配置
# 打开 src/scripts/constant.ts 文件
# 将 EXTENSION_ID 改为您的实际扩展ID
```

在 `src/scripts/constant.ts` 中修改：
```typescript
export const EXTENSION_ID = 'nflcmnkchaaogfjpcdnlgmleblhhmhdg'; // 替换为您的扩展ID
```

### 4.2 重新构建并注册

```powershell
# 重新构建
pnpm run build

# 注册Native Messaging Host
pnpm run register:dev
```

### 4.3 验证注册成功

成功注册后会看到：
```
✓ Successfully created Windows registry entry
Successfully registered user-level Native Messaging host!
```

注册信息：
- **主机名称**: `com.chromemcp.nativehost`
- **通信端口**: 12306
- **扩展ID**: 您的实际扩展ID

## 🤖 第五步：安装和配置AI环境

### 5.1 安装Ollama

**Ollama** 是一个轻量级、易用的本地AI模型运行工具。

```bash
# Windows
winget install Ollama.Ollama

# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.ai/install.sh | sh
```

### 5.2 启动Ollama服务

```bash
# 启动Ollama服务
ollama serve

# 验证安装
ollama --version
```

### 5.3 下载AI模型

```bash
# 推荐模型（按性能和大小排序）

# 1. Llama 3.1 8B（推荐，平衡性能和速度）
ollama pull llama3.1:8b

# 2. Qwen2.5 7B（中文优化）
ollama pull qwen2.5:7b

# 3. Llama 3.1 70B（最强性能，需要大内存）
ollama pull llama3.1:70b

# 4. 视觉模型（支持图像理解）
ollama pull llava:13b
```

### 5.4 安装Open WebUI

**Open WebUI** 提供现代化的Web界面来使用Ollama模型。

```bash
# 方法1：使用pip安装（推荐）
pip install open-webui

# 方法2：使用Docker
docker run -d -p 3000:8080 \
  --add-host=host.docker.internal:host-gateway \
  -v open-webui:/app/backend/data \
  --name open-webui \
  --restart always \
  ghcr.io/open-webui/open-webui:main
```

### 5.5 配置MCP连接

**方法A：环境变量配置**
```bash
# 设置MCP服务器
export OPENWEBUI_MCP_SERVERS='{"chrome-mcp-server": {"url": "http://127.0.0.1:12306/mcp", "name": "Chrome Browser Controller"}}'

# 启动Open WebUI
open-webui serve --port 3000
```

**方法B：配置文件**
创建配置文件 `~/.config/open-webui/config.json`：
```json
{
  "mcp": {
    "servers": {
      "chrome-mcp-server": {
        "url": "http://127.0.0.1:12306/mcp",
        "name": "Chrome Browser Controller",
        "description": "Control Chrome browser with 23 tools"
      }
    }
  },
  "ollama": {
    "base_url": "http://localhost:11434"
  }
}
```

## 🚀 第六步：启动系统

### 6.1 启动Ollama服务

```bash
# 启动Ollama（如果还没启动）
ollama serve

# 验证模型可用
ollama list
```

### 6.2 启动Native Server

```bash
# 进入native-server目录
cd mcp-chrome-master/app/native-server

# 启动MCP服务器（会在后台等待Chrome扩展连接）
node dist/index.js
```

### 6.3 启动Open WebUI

```bash
# 方法1：直接启动
open-webui serve --port 3000

# 方法2：带MCP配置启动
OPENWEBUI_MCP_SERVERS='{"chrome-mcp-server": {"url": "http://127.0.0.1:12306/mcp"}}' open-webui serve --port 3000

# 方法3：Docker启动
docker start open-webui  # 如果使用Docker安装
```

### 6.4 测试语义引擎（本地模式）

1. **打开Chrome扩展弹窗**
2. **查看语义引擎状态**
   - 应显示："语义引擎已就绪"
   - 模型：multilingual-e5-small 或 multilingual-e5-base
   - 状态：excellent

3. **如果需要切换模型**
   - 在扩展弹窗中选择不同模型
   - 点击"重新初始化"
   - 等待几秒钟完成切换

### 6.5 连接Chrome扩展

1. **点击Chrome工具栏中的扩展图标**
2. **查看Native Server配置部分**
   - 应显示："🟢 已连接，服务已启动"
   - 连接端口：12306
3. **如果显示"服务未启动"，点击"连接"按钮**

### 6.6 配置Open WebUI

1. **打开浏览器访问**：`http://localhost:3000`
2. **首次设置**：
   - 创建管理员账户
   - 选择Ollama作为模型提供商
   - 确认可以看到您下载的模型
3. **配置MCP工具**：
   - 进入设置 → 工具/插件
   - 添加MCP服务器：`http://127.0.0.1:12306/mcp`
   - 启用Chrome控制工具

## 🧪 第七步：测试系统

### 7.1 基础连接测试

在Open WebUI聊天界面发送：
```
你现在可以使用哪些工具？请列出所有可用的Chrome控制工具和它们的功能。
```

### 7.2 Chrome控制测试

```
请帮我获取当前Chrome浏览器中所有打开的标签页信息
```

### 7.3 截图分析测试

```
请帮我截取当前Chrome页面的截图，然后分析截图中的内容
```

### 7.4 导航测试

```
请帮我在Chrome中打开百度首页
```

### 7.5 语义搜索测试

```
请在我当前打开的所有标签页中搜索与"人工智能"相关的内容
```

## 🔍 第八步：语义搜索功能（本地模式）

> 🎯 **优势**：本地模式无需网络下载，启动更快更稳定！

### 8.1 验证语义搜索功能

1. **检查扩展状态**
   - 打开Chrome扩展弹窗
   - 语义引擎部分应显示："语义引擎已就绪"
   - 模型状态：excellent

2. **如果显示初始化中**
   - 等待几秒钟让本地模型加载完成
   - 本地模式比在线下载快很多

### 8.2 模型选择建议

- **multilingual-e5-small (118MB)**
  - ✅ 轻量级，启动快
  - ✅ 适合大多数使用场景
  - ✅ 推荐日常使用

- **multilingual-e5-base (279MB)**
  - ✅ 更好的搜索效果
  - ✅ 适合专业用途
  - ⚠️ 占用更多内存

### 8.3 切换模型

```bash
# 如果需要切换模型，在扩展弹窗中：
1. 选择不同的模型
2. 点击"重新初始化"
3. 等待几秒钟完成切换（本地模式很快）
```

### 8.4 故障排除

如果遇到 `FS.syncfs` 警告：
- ✅ 这是正常现象，不影响功能
- ✅ 是WASM文件系统的内部优化提示
- ✅ 可以安全忽略

## ✅ 验证部署成功

如果看到以下结果，说明部署成功：

1. **✅ Ollama服务正常运行**
2. **✅ Open WebUI可以访问并显示模型**
3. **✅ AI能识别23个Chrome控制工具**
4. **✅ 成功获取标签页信息**
5. **✅ 成功截图并分析内容**
6. **✅ 成功导航到指定网页**
7. **✅ 语义搜索功能正常**

## 🎯 常见功能示例

### 基础自动化
```
"请帮我打开淘宝首页"
"关闭所有包含'广告'的标签页"
"在当前页面搜索'人工智能'"
```

### 智能分析
```
"分析当前页面的主要内容"
"提取页面中的所有链接"
"找到页面中的表单并描述"
```

### 高级操作
```
"监控这个页面的网络请求"
"注入脚本隐藏所有广告"
"自动填写登录表单"
```

## 🔧 故障排除

### 问题1：Ollama连接失败
**解决方案**：
```bash
# 检查Ollama服务状态
ollama list

# 重启Ollama服务
ollama serve

# 检查端口占用
netstat -an | grep 11434
```

### 问题2：Open WebUI无法访问
**解决方案**：
```bash
# 检查Open WebUI状态
ps aux | grep open-webui

# 重启Open WebUI
open-webui serve --port 3000

# 检查端口占用
netstat -an | grep 3000
```

### 问题3：MCP工具不显示
**解决方案**：
1. 检查mcp-chrome服务器是否运行
2. 重启Open WebUI
3. 确认Chrome扩展已连接
4. 检查MCP配置：
   ```bash
   curl http://127.0.0.1:12306/health
   ```

### 问题4：语义搜索引擎问题 🔧
**现象**：语义引擎显示初始化中或错误

**本地模式解决方案**：
1. **检查模型文件**
   ```bash
   # 检查模型文件是否存在
   ls mcp-chrome-master/app/chrome-extension/.output/chrome-mv3/models/Xenova/
   ```

2. **重新运行配置脚本**
   ```bash
   # 重新配置本地模型
   powershell -ExecutionPolicy Bypass -File setup-local-models.ps1
   ```

3. **手动重试**
   - 在扩展中点击"重新初始化"
   - 本地模式通常几秒钟就完成

4. **检查扩展权限**
   - 确保扩展有访问本地文件的权限
   - 重新加载扩展

5. **忽略FS.syncfs警告**
   - 这是正常的WASM文件系统警告
   - 不影响功能使用

### 问题5：Chrome扩展无法连接
**解决方案**：
1. 检查Native Messaging注册
2. 重新启动Chrome
3. 重新加载扩展
4. 验证扩展ID配置正确

### 问题6：服务器端口被占用
**现象**：无法启动在端口12306或3000
**解决方案**：
```bash
# 检查端口占用
netstat -an | grep "12306\|3000"

# Linux/macOS 结束进程
sudo lsof -ti:12306 | xargs kill -9
sudo lsof -ti:3000 | xargs kill -9

# Windows 结束进程
netstat -ano | findstr "12306"
taskkill /f /pid [进程ID]
```

### 问题7：模型下载失败
**解决方案**：
```bash
# 重新下载模型
ollama pull llama3.1:8b

# 检查磁盘空间
df -h  # Linux/macOS
dir   # Windows

# 清理旧模型
ollama rm [model_name]
```

## 📚 进阶使用

### 自定义脚本注入
```
"请注入以下CSS样式隐藏所有广告：.ad { display: none !important; }"
```

### 批量数据收集
```
"请收集当前页面所有商品的价格信息"
```

### 智能书签管理
```
"请将当前页面添加到'技术文档'书签文件夹"
```

## 🎉 恭喜！

你现在拥有了一个完整的AI控制Chrome浏览器自动化系统！

这个系统可以：
- 🤖 理解自然语言指令
- 👀 "看懂"网页内容（使用视觉模型）
- 🔍 智能搜索和分析
- ⚡ 执行复杂的自动化任务
- 🛡️ 完全本地运行，保护隐私
- 🚀 基于Ollama，轻量高效
- 🌐 现代化Web界面，易于使用

## 🎯 使用技巧

### 启动顺序
```bash
# 1. 启动Ollama
ollama serve

# 2. 启动MCP服务器
cd mcp-chrome-master/app/native-server
node dist/index.js

# 3. 启动Open WebUI
open-webui serve --port 3000

# 4. 访问 http://localhost:3000 开始使用
```

### 常用命令示例
```
"获取当前所有标签页信息"
"截取当前页面截图并分析内容"
"在所有标签页中搜索'人工智能'相关内容"
"打开GitHub首页"
"关闭包含'广告'的标签页"
"填写页面中的登录表单"
```

### 模型推荐
- **日常使用**: `llama3.1:8b` - 平衡性能和速度
- **中文优化**: `qwen2.5:7b` - 更好的中文理解
- **视觉理解**: `llava:13b` - 支持图像分析
- **最强性能**: `llama3.1:70b` - 需要大内存

享受你的AI浏览器助手吧！🚀
