#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/ts-jest@29.3.4_@babel+core@_c36c044b91565ec7e4cf126b17b044a4/node_modules/ts-jest/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/ts-jest@29.3.4_@babel+core@_c36c044b91565ec7e4cf126b17b044a4/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/ts-jest@29.3.4_@babel+core@_c36c044b91565ec7e4cf126b17b044a4/node_modules/ts-jest/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/ts-jest@29.3.4_@babel+core@_c36c044b91565ec7e4cf126b17b044a4/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
