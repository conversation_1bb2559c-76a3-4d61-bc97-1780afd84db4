// Generated by wxt
import "wxt/browser";

declare module "wxt/browser" {
  export type PublicPath =
    | ""
    | "/"
    | "/background.js"
    | "/content-scripts/content.js"
    | "/icon/128.png"
    | "/icon/16.png"
    | "/icon/32.png"
    | "/icon/48.png"
    | "/icon/96.png"
    | "/libs/ort.min.js"
    | "/models/Xenova/multilingual-e5-base/config.json"
    | "/models/Xenova/multilingual-e5-base/model_quantized.onnx"
    | "/models/Xenova/multilingual-e5-base/special_tokens_map.json"
    | "/models/Xenova/multilingual-e5-base/tokenizer.json"
    | "/models/Xenova/multilingual-e5-base/tokenizer_config.json"
    | "/models/Xenova/multilingual-e5-small/config.json"
    | "/models/Xenova/multilingual-e5-small/model_quantized.onnx"
    | "/models/Xenova/multilingual-e5-small/special_tokens_map.json"
    | "/models/Xenova/multilingual-e5-small/tokenizer.json"
    | "/models/Xenova/multilingual-e5-small/tokenizer_config.json"
    | "/offscreen.html"
    | "/popup.html"
    | "/workers/ort-wasm-simd-threaded.jsep.mjs"
    | "/workers/ort-wasm-simd-threaded.jsep.wasm"
    | "/workers/ort-wasm-simd-threaded.mjs"
    | "/workers/ort-wasm-simd-threaded.wasm"
    | "/workers/simd_math.js"
    | "/workers/simd_math_bg.wasm"
    | "/workers/similarity.worker.js"
    | "/wxt.svg"
  type HtmlPublicPath = Extract<PublicPath, `${string}.html`>
  export interface WxtRuntime {
    getURL(path: PublicPath): string;
    getURL(path: `${HtmlPublicPath}${string}`): string;
  }
}
