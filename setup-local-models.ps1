# Chrome MCP Server Local Models Setup Script
# Download models + Configure + Rebuild extension

Write-Host "Chrome MCP Server Local Models Setup" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Configure local model mode to solve network dependency issues" -ForegroundColor Yellow
Write-Host ""

# Check project directory
$projectDir = "mcp-chrome-master\app\chrome-extension"
if (-not (Test-Path $projectDir)) {
    Write-Host "Project directory not found: $projectDir" -ForegroundColor Red
    Write-Host "Please ensure you are running this script in the correct directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit..."
    exit
}

Write-Host "Found project directory: $projectDir" -ForegroundColor Green
Write-Host ""

# Step 1: Create model directory structure
Write-Host "Step 1: Creating model directory structure..." -ForegroundColor Yellow

$modelsDir = "$projectDir\public\models\Xenova"
$smallModelDir = "$modelsDir\multilingual-e5-small"
$baseModelDir = "$modelsDir\multilingual-e5-base"

New-Item -ItemType Directory -Force -Path $modelsDir | Out-Null
New-Item -ItemType Directory -Force -Path $smallModelDir | Out-Null
New-Item -ItemType Directory -Force -Path $baseModelDir | Out-Null

Write-Host "Directory structure created successfully" -ForegroundColor Green
Write-Host ""

# Step 2: Copy local model files (no network download)
Write-Host "Step 2: Copying local model files from 'xiazaide'..." -ForegroundColor Yellow

$sourceBase = "xiazaide"
$smallSource = "$sourceBase\multilingual-e5-small"
$baseSource  = "$sourceBase\multilingual-e5-base"

$requiredFiles = @(
    "model_quantized.onnx",
    "tokenizer.json",
    "tokenizer_config.json",
    "config.json",
    "special_tokens_map.json"
)

function Copy-ModelFiles($sourceDir, $destDir, $name) {
    if (-not (Test-Path $sourceDir)) {
        Write-Host "Source not found for $($name): $sourceDir" -ForegroundColor Red
        return $false
    }

    $allExist = $true
    foreach ($f in $requiredFiles) {
        if (-not (Test-Path (Join-Path $sourceDir $f))) {
            Write-Host "MISSING in $($name): $f" -ForegroundColor Red
            $allExist = $false
        }
    }

    if (-not $allExist) {
        Write-Host "Please ensure all required files exist in: $sourceDir" -ForegroundColor Yellow
        return $false
    }

    Write-Host "Copying $name files..." -ForegroundColor Cyan
    Copy-Item -Recurse -Force (Join-Path $sourceDir '*') $destDir

    # Verify copy
    $ok = $true
    foreach ($f in $requiredFiles) {
        if (-not (Test-Path (Join-Path $destDir $f))) {
            Write-Host "FAILED COPY: $name => $f not found in destination" -ForegroundColor Red
            $ok = $false
        }
    }
    if ($ok) {
        Write-Host "$name copied successfully" -ForegroundColor Green
    }
    return $ok
}

$okSmall = Copy-ModelFiles -sourceDir $smallSource -destDir $smallModelDir -name "multilingual-e5-small"
$okBase  = Copy-ModelFiles -sourceDir $baseSource  -destDir $baseModelDir  -name "multilingual-e5-base"

if (-not ($okSmall -and $okBase)) {
    Write-Host "One or more model copies failed. Please fix the issues above and re-run." -ForegroundColor Red
    Read-Host "Press Enter to exit..."
    exit 1
}

# Ensure workers assets are under public to avoid build-time missing file issues
$workersSrc = Join-Path $projectDir "workers"
$workersPublic = Join-Path $projectDir "public\workers"
if (Test-Path $workersSrc) {
    if (-not (Test-Path $workersPublic)) { New-Item -ItemType Directory -Force -Path $workersPublic | Out-Null }
    Write-Host "Copying workers to public/workers..." -ForegroundColor Cyan
    Copy-Item -Recurse -Force (Join-Path $workersSrc '*') $workersPublic
}

Write-Host ""

# Step 3: Install dependencies (pnpm, workspace compatible) and build
Write-Host "Step 3: Installing dependencies and building project (pnpm)..." -ForegroundColor Yellow

# Ensure pnpm is available via corepack or global install
function Ensure-Pnpm() {
    $pnpmVersion = & cmd /c pnpm -v 2>$null
    if ($LASTEXITCODE -ne 0 -or -not $pnpmVersion) {
        Write-Host "pnpm not found, trying to activate via corepack..." -ForegroundColor Yellow
        & cmd /c "corepack enable && corepack prepare pnpm@9.12.2 --activate" | Out-Null
        $pnpmVersion2 = & cmd /c pnpm -v 2>$null
        if ($LASTEXITCODE -ne 0 -or -not $pnpmVersion2) {
            Write-Host "corepack activation failed. Trying global install..." -ForegroundColor Yellow
            & cmd /c "npm install -g pnpm" | Out-Null
        }
    }
    $pnpmVersionFinal = & cmd /c pnpm -v
    if ($LASTEXITCODE -ne 0 -or -not $pnpmVersionFinal) {
        Write-Host "Failed to install/activate pnpm. Please install pnpm manually and re-run." -ForegroundColor Red
        Read-Host "Press Enter to exit..."
        exit 1
    }
    Write-Host "Using pnpm version: $pnpmVersionFinal" -ForegroundColor Green
}

Ensure-Pnpm

# Install dependencies at repo root to resolve workspace:*
# Repo root is the folder containing pnpm-workspace.yaml (mcp-chrome-master)
$repoRoot = Join-Path (Resolve-Path ".") "mcp-chrome-master"
if (-not (Test-Path (Join-Path $repoRoot "pnpm-workspace.yaml"))) {
    # Fallback: if running from repo root already
    if (Test-Path (Join-Path (Resolve-Path ".") "pnpm-workspace.yaml")) {
        $repoRoot = (Resolve-Path ".").Path
    } else {
        Write-Host "Could not locate repo root (pnpm-workspace.yaml). Please run this script from the project root containing 'mcp-chrome-master'." -ForegroundColor Red
        Read-Host "Press Enter to exit..."
        exit 1
    }
}

Push-Location $repoRoot
Write-Host "Installing dependencies at repo root (filtered to shared + chrome-mcp-server): $repoRoot" -ForegroundColor Cyan
& cmd /c "pnpm --filter chrome-mcp-shared --filter chrome-mcp-server install"
if ($LASTEXITCODE -ne 0) {
    Pop-Location
    Write-Host "pnpm install failed" -ForegroundColor Red
    Read-Host "Press Enter to exit..."
    exit 1
}

# Build shared package first (provides chrome-mcp-shared/dist)
Write-Host "Building shared package (chrome-mcp-shared)..." -ForegroundColor Cyan
& cmd /c "pnpm --filter chrome-mcp-shared build"
if ($LASTEXITCODE -ne 0) {
    Pop-Location
    Write-Host "Build shared package failed" -ForegroundColor Red
    Read-Host "Press Enter to exit..."
    exit 1
}

# Build chrome extension package
Write-Host "Building chrome extension..." -ForegroundColor Cyan
& cmd /c "pnpm --filter chrome-mcp-server build"
$buildExit = $LASTEXITCODE
Pop-Location

if ($buildExit -ne 0) {
    Write-Host "Build failed" -ForegroundColor Red
    Read-Host "Press Enter to exit..."
    exit 1
}

Write-Host "Project build completed" -ForegroundColor Green
Write-Host ""

# Step 4: Verify build results
Write-Host "Step 4: Verifying build results..." -ForegroundColor Yellow

# wxt default output path for chrome mv3
$builtExtensionDir = "mcp-chrome-master\app\chrome-extension\.output\chrome-mv3"
$builtModelsDir = "$builtExtensionDir\models\Xenova"

if (Test-Path "$builtModelsDir\multilingual-e5-small\model_quantized.onnx") {
    Write-Host "multilingual-e5-small found in build directory" -ForegroundColor Green
} else {
    Write-Host "multilingual-e5-small not found in build directory" -ForegroundColor Red
}

if (Test-Path "$builtModelsDir\multilingual-e5-base\model_quantized.onnx") {
    Write-Host "multilingual-e5-base found in build directory" -ForegroundColor Green
} else {
    Write-Host "multilingual-e5-base not found in build directory" -ForegroundColor Red
}

Write-Host ""

# Display final status
Write-Host "Configuration Status:" -ForegroundColor Yellow
Write-Host ""
Write-Host "Source directory structure:" -ForegroundColor Cyan
Write-Host "$projectDir\public\models\Xenova\" -ForegroundColor Gray
Write-Host "├── multilingual-e5-small\(model files)" -ForegroundColor Gray
Write-Host "└── multilingual-e5-base\(model files)" -ForegroundColor Gray
Write-Host ""
Write-Host "Build directory structure:" -ForegroundColor Cyan
Write-Host "$builtExtensionDir\models\Xenova\" -ForegroundColor Gray
Write-Host "├── multilingual-e5-small\(model files)" -ForegroundColor Gray
Write-Host "└── multilingual-e5-base\(model files)" -ForegroundColor Gray

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. In Chrome open: chrome://extensions" -ForegroundColor Cyan
Write-Host "2. Enable 'Developer mode'" -ForegroundColor Cyan
Write-Host "3. Click 'Load unpacked' and select: $builtExtensionDir" -ForegroundColor Cyan
Write-Host "4. Open extension popup and click 'Reinitialize'" -ForegroundColor Cyan
Write-Host "5. It will use local files, no network download needed" -ForegroundColor Cyan

Write-Host ""
Write-Host "Local Mode Advantages:" -ForegroundColor Yellow
Write-Host "- No network dependency" -ForegroundColor Green
Write-Host "- Faster startup" -ForegroundColor Green
Write-Host "- No need to re-download after browser reinstall" -ForegroundColor Green
Write-Host "- Model files distributed with extension" -ForegroundColor Green

Write-Host ""
$hasSmall = Test-Path "$builtModelsDir\multilingual-e5-small\model_quantized.onnx"
$hasBase = Test-Path "$builtModelsDir\multilingual-e5-base\model_quantized.onnx"

if ($hasSmall -or $hasBase) {
    Write-Host "Local model configuration successful!" -ForegroundColor Green
    Write-Host "Semantic engine will now use local files instead of network downloads." -ForegroundColor Green
} else {
    Write-Host "Configuration not fully successful, please re-check local model files under: $projectDir\public\models\Xenova" -ForegroundColor Yellow
}

Read-Host "Press Enter to exit..."
