#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_a30de4d2bd87678e9fe578ff685653d6/node_modules/tsup/dist/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_a30de4d2bd87678e9fe578ff685653d6/node_modules/tsup/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_a30de4d2bd87678e9fe578ff685653d6/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_a30de4d2bd87678e9fe578ff685653d6/node_modules/tsup/dist/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_a30de4d2bd87678e9fe578ff685653d6/node_modules/tsup/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_a30de4d2bd87678e9fe578ff685653d6/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../tsup/dist/cli-node.js" "$@"
else
  exec node  "$basedir/../tsup/dist/cli-node.js" "$@"
fi
