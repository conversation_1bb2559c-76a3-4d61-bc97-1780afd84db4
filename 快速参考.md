# Chrome MCP Server 快速参考

## 🚀 快速开始

### 1. 安装基础环境
```bash
# 安装Node.js, Python, pnpm
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh  # Linux/macOS
# 或 winget install Ollama.Ollama  # Windows
```

### 2. 一键部署Chrome MCP
```bash
# 下载项目并解压到 D:\mcp-chrome-master
# 将模型文件放到 xiazaide 文件夹
# 运行一键配置脚本
powershell -ExecutionPolicy Bypass -File setup-local-models.ps1
```

### 3. 安装AI环境
```bash
# 下载AI模型
ollama pull llama3.1:8b

# 安装Open WebUI
pip install open-webui
```

### 4. 加载Chrome扩展
1. 打开 `chrome://extensions/`
2. 启用"开发者模式"
3. 加载 `mcp-chrome-master\app\chrome-extension\.output\chrome-mv3`

### 5. 配置Native Server
```bash
cd mcp-chrome-master/app/native-server
# 修改 src/scripts/constant.ts 中的扩展ID
pnpm run build
pnpm run register:dev
```

### 6. 启动系统
```bash
# 1. 启动Ollama
ollama serve

# 2. 启动Native Server
node dist/index.js

# 3. 启动Open WebUI
open-webui serve --port 3000

# 4. 访问 http://localhost:3000
```

## 🛠️ 常用命令

### Ollama命令
```bash
# 启动Ollama服务
ollama serve

# 下载模型
ollama pull llama3.1:8b
ollama pull qwen2.5:7b
ollama pull llava:13b

# 查看已安装模型
ollama list

# 删除模型
ollama rm model_name

# 运行模型（测试）
ollama run llama3.1:8b
```

### Open WebUI命令
```bash
# 启动Open WebUI
open-webui serve --port 3000

# 带MCP配置启动
OPENWEBUI_MCP_SERVERS='{"chrome-mcp-server": {"url": "http://127.0.0.1:12306/mcp"}}' open-webui serve --port 3000

# Docker方式启动
docker run -d -p 3000:8080 --name open-webui ghcr.io/open-webui/open-webui:main
```

### 构建命令
```bash
# 构建所有组件
pnpm run build

# 构建Chrome扩展
cd app/chrome-extension && pnpm run build

# 构建Native Server
cd app/native-server && pnpm run build

# 构建共享包
pnpm --filter chrome-mcp-shared build
```

### 开发命令
```bash
# 开发模式
cd app/chrome-extension && pnpm run dev
cd app/native-server && pnpm run dev

# 运行测试
pnpm run test

# 代码格式化
pnpm run format
```

### Native Messaging
```bash
# 注册开发版本
pnpm run register:dev

# 注册生产版本
node dist/cli.js register

# 修复权限
node dist/cli.js fix-permissions

# 更新端口
node dist/cli.js update-port 12306
```

## 📁 目录结构

```
mcp-chrome-master/
├── app/
│   ├── chrome-extension/          # Chrome扩展
│   │   ├── entrypoints/          # 入口点
│   │   │   ├── background/       # Service Worker
│   │   │   ├── popup/           # 弹窗界面
│   │   │   ├── content/         # 内容脚本
│   │   │   └── offscreen/       # 离屏文档
│   │   ├── utils/               # 工具类
│   │   │   ├── semantic-similarity-engine.ts
│   │   │   ├── vector-database.ts
│   │   │   └── content-indexer.ts
│   │   └── .output/chrome-mv3/  # 构建输出
│   └── native-server/            # Native服务器
│       ├── src/
│       │   ├── server/          # HTTP服务器
│       │   ├── mcp/             # MCP协议
│       │   ├── scripts/         # 脚本工具
│       │   └── cli.ts           # 命令行工具
│       └── dist/                # 构建输出
├── packages/
│   ├── shared/                  # 共享包
│   └── wasm-simd/              # WASM模块
├── xiazaide/                    # 本地模型文件
│   └── Xenova/
│       ├── multilingual-e5-small/
│       └── multilingual-e5-base/
└── setup-local-models.ps1      # 一键配置脚本
```

## 🔧 配置文件

### Chrome扩展配置
**文件**: `app/chrome-extension/wxt.config.ts`
```typescript
export default defineConfig({
  manifest: {
    permissions: ['nativeMessaging', 'tabs', 'activeTab', ...],
    host_permissions: ['<all_urls>']
  }
});
```

### 语义引擎配置
**文件**: `app/chrome-extension/utils/semantic-similarity-engine.ts`
```typescript
const config = {
  useLocalFiles: true,
  modelIdentifier: 'Xenova/multilingual-e5-small',
  dimension: 384,
  numThreads: 1
};
```

### Native Server配置
**文件**: `app/native-server/src/scripts/constant.ts`
```typescript
export const EXTENSION_ID = 'your-extension-id';
export const HOST_NAME = 'com.chromemcp.nativehost';
```

## 🎯 核心API

### MCP工具调用
```javascript
// 获取标签页
const tabs = await callTool('get_tabs');

// 截图
const screenshot = await callTool('take_screenshot', {
  tabId: 123,
  fullPage: true
});

// 语义搜索
const results = await callTool('semantic_search', {
  query: '人工智能',
  limit: 10
});
```

### 语义引擎API
```typescript
// 初始化
const engine = new SemanticSimilarityEngine(config);
await engine.initialize();

// 文本向量化
const vector = await engine.embedText('测试文本');

// 相似度搜索
const results = await engine.searchSimilar('查询文本');
```

### 向量数据库API
```typescript
// 添加文档
await vectorDB.addDocument({
  content: '文档内容',
  metadata: { url: 'https://example.com' }
});

// 搜索
const results = await vectorDB.search('查询');
```

## 🚨 故障排除

### 常见问题

**Ollama连接失败**
```bash
# 检查Ollama状态
ollama list
ps aux | grep ollama

# 重启Ollama
ollama serve

# 检查端口
netstat -an | grep 11434
```

**Open WebUI无法访问**
```bash
# 检查Open WebUI状态
ps aux | grep open-webui

# 重启Open WebUI
open-webui serve --port 3000

# 检查端口
netstat -an | grep 3000
```

**语义引擎初始化失败**
```bash
# 检查模型文件
ls app/chrome-extension/.output/chrome-mv3/models/

# 重新配置
powershell -ExecutionPolicy Bypass -File setup-local-models.ps1

# 清理缓存
# 在扩展中点击"清空所有数据"
```

**Native Server连接失败**
```bash
# 检查扩展ID
cat app/native-server/src/scripts/constant.ts

# 重新注册
cd app/native-server
pnpm run build
pnpm run register:dev

# 检查注册表（Windows）
reg query "HKCU\Software\Google\Chrome\NativeMessagingHosts"
```

**端口被占用**
```bash
# 检查端口
netstat -an | grep "12306\|3000\|11434"

# 结束进程（Linux/macOS）
sudo lsof -ti:12306 | xargs kill -9

# 结束进程（Windows）
taskkill /f /pid [PID]
```

### 日志查看

**Chrome扩展日志**
1. 打开 `chrome://extensions/`
2. 点击扩展的"检查视图"
3. 查看 Service Worker 控制台

**Native Server日志**
```powershell
# 启动时查看控制台输出
node dist/index.js
```

**语义引擎日志**
1. 在扩展检查视图中
2. 切换到 Offscreen 页面
3. 查看控制台日志

## 📊 性能监控

### 内存使用
- ONNX模型: ~200MB
- 向量索引: ~50MB
- 总内存: <500MB

### 性能指标
- 模型加载: <5秒
- 文本向量化: >100 tokens/秒
- 向量搜索: <100ms

### 优化建议
- 使用 e5-small 模型（更快）
- 定期清理向量索引
- 限制并发操作数量

## 🔄 版本升级

### 升级Chrome扩展
```powershell
cd app/chrome-extension
pnpm run build
# 在Chrome中重新加载扩展
```

### 升级Native Server
```powershell
cd app/native-server
pnpm run build
pnpm run register:dev
# 重启服务
```

### 升级模型
```powershell
# 更新模型文件到 xiazaide/
# 重新运行配置脚本
powershell -ExecutionPolicy Bypass -File setup-local-models.ps1
```

## 🎨 自定义配置

### 修改模型
1. 下载新模型到 `xiazaide/Xenova/model-name/`
2. 修改 `semantic-similarity-engine.ts` 中的配置
3. 重新构建扩展

### 添加新工具
1. 在 `native-server/src/mcp/` 中添加工具定义
2. 在 `chrome-extension/` 中实现工具逻辑
3. 重新构建两个组件

### 修改UI
1. 编辑 `app/chrome-extension/entrypoints/popup/`
2. 修改Vue组件和样式
3. 重新构建扩展

## 📚 学习资源

### 文档
- [技术文档](./技术文档.md) - 详细技术说明
- [API文档](./API文档.md) - 完整API参考
- [部署文档](./部署.md) - 部署指南

### 示例代码
```javascript
// 自动化示例
async function automateTask() {
  // 打开页面
  const tab = await callTool('create_tab', {
    url: 'https://example.com'
  });
  
  // 等待加载
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 截图分析
  const screenshot = await callTool('take_screenshot', {
    tabId: tab.id
  });
  
  // 语义搜索
  const results = await callTool('semantic_search', {
    query: '重要信息',
    tabIds: [tab.id]
  });
  
  return results;
}
```

### 调试技巧
- 使用Chrome DevTools调试扩展
- 在Native Server中添加console.log
- 监控网络请求和响应
- 检查IndexedDB中的数据

---

## 🎯 快速命令备忘

```bash
# 完整系统启动
ollama serve &
cd mcp-chrome-master/app/native-server && node dist/index.js &
open-webui serve --port 3000

# 完整重新部署
powershell -ExecutionPolicy Bypass -File setup-local-models.ps1

# 重新加载扩展
# chrome://extensions/ → 重新加载

# 重启各组件
# Ollama: Ctrl+C 停止，然后 ollama serve
# Native Server: Ctrl+C 停止，然后 node dist/index.js
# Open WebUI: Ctrl+C 停止，然后 open-webui serve --port 3000

# 查看日志
# 扩展日志: chrome://extensions/ → 检查视图 → Service Worker
# Ollama日志: ollama logs
# Open WebUI日志: 控制台输出

# 检查连接状态
# Open WebUI: http://localhost:3000
# MCP健康检查: curl http://127.0.0.1:12306/health
# 扩展状态: 扩展弹窗 → Native Server配置

# 常用测试命令
# "你现在可以使用哪些工具？"
# "获取当前浏览器的所有标签页"
# "截取当前页面截图并分析"
```
