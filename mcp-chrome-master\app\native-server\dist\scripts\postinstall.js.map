{"version": 3, "file": "postinstall.js", "sourceRoot": "", "sources": ["../../src/scripts/postinstall.ts"], "names": [], "mappings": ";;;;;;AAEA,4CAAoB;AACpB,4CAAoB;AACpB,gDAAwB;AACxB,yCAA0C;AAC1C,mCAA8D;AAE9D,uCAAuC;AACvC,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC;AAE5C,mDAAmD;AACnD,SAAS,mBAAmB;IAC1B,6BAA6B;IAC7B,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,MAAM,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,yBAAyB;IACzB,mEAAmE;IACnE,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gEAAgE;IAChE,4FAA4F;IAC5F,gDAAgD;IAChD,MAAM,kBAAkB,GACtB,OAAO,CAAC,QAAQ,KAAK,OAAO;QAC1B,CAAC,CAAC,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,4BAA4B,CAAC;QACvE,CAAC,CAAC,CAAC,eAAe,EAAE,qBAAqB,EAAE,eAAe,CAAC,CAAC;IAEhE,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6CAA6C;IAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACvF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,uDAAuD;IACvD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,MAAM,qBAAqB,GAAG;YAC5B,uBAAuB;YACvB,yCAAyC;YACzC,yCAAyC;YACzC,0BAA0B;SAC3B,CAAC;QAEF,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YACzE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,eAAe,GAAG,mBAAmB,EAAE,CAAC;AAE9C;;GAEG;AACH,KAAK,UAAU,aAAa;IAC1B,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;QAEjE,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,yBAAyB,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QACpE,YAAE,CAAC,aAAa,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,6CAA6C,EAAE,OAAO,CAAC,CAAC,CAAC;IACjF,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,IAAI,CAAC,IAAA,iBAAS,EAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IACzF,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,0BAA0B;IACvC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,eAAe;QACf,MAAM,4BAA4B,EAAE,CAAC;QACrC,OAAO;IACT,CAAC;IAED,kBAAkB;IAClB,MAAM,YAAY,GAAG;QACnB,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC;QACtC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC;QACzC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC;KACrC,CAAC;IAEF,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE,CAAC;QACpC,IAAI,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,YAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC9B,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EAAC,mCAAmC,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,CACjF,CAAC;YACJ,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,OAAO,CAAC,IAAI,CACV,IAAA,iBAAS,EACP,8CAA8C,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,EACvF,QAAQ,CACT,CACF,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,IAAA,iBAAS,EAAC,sBAAsB,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,4BAA4B;IACzC,MAAM,YAAY,GAAG;QACnB,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC;QACtC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,cAAc,CAAC;QAC1C,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC;KACrC,CAAC;IAEF,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE,CAAC;QACpC,IAAI,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,uBAAuB;gBACvB,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACpC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;oBACvC,QAAQ;oBACR,WAAW;oBACX,YAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;oBACxD,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EAAC,sCAAsC,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,CACpF,CAAC;gBACJ,CAAC;gBAED,UAAU;gBACV,YAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,YAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC3C,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EAAC,qCAAqC,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,CACnF,CAAC;YACJ,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,OAAO,CAAC,IAAI,CACV,IAAA,iBAAS,EACP,4CAA4C,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,EACrF,QAAQ,CACT,CACF,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,IAAA,iBAAS,EAAC,sBAAsB,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,UAAU,qBAAqB;IAClC,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,wDAAwD,EAAE,MAAM,CAAC,CAAC,CAAC;QAEzF,uEAAuE;QACvE,MAAM,0BAA0B,EAAE,CAAC;QAEnC,IAAI,eAAe,EAAE,CAAC;YACpB,uEAAuE;YACvE,MAAM,gBAAgB,GAAG,MAAM,IAAA,gCAAwB,GAAE,CAAC;YAE1D,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,iEAAiE;gBACjE,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EACP,yEAAyE,EACzE,QAAQ,CACT,CACF,CAAC;gBACF,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EAAC,iEAAiE,EAAE,MAAM,CAAC,CACrF,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,KAAK,uBAAY,oBAAoB,CAAC,CAAC;gBACnD,uBAAuB,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,gEAAgE;YAChE,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EAAC,8DAA8D,EAAE,QAAQ,CAAC,CACpF,CAAC;YACF,uBAAuB,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EACP,cAAc,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EACtE,KAAK,CACN,CACF,CAAC;QACF,uBAAuB,EAAE,CAAC;IAC5B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB;IAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,IAAA,iBAAS,EAAC,uCAAuC,EAAE,MAAM,CAAC,CAAC,CAAC;IAE/E,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,+CAA+C,EAAE,QAAQ,CAAC,CAAC,CAAC;IAClF,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,KAAK,uBAAY,WAAW,CAAC,CAAC;IAC5C,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,SAAS,uBAAY,WAAW,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EAAC,uEAAuE,EAAE,QAAQ,CAAC,CAC7F,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,uDAAuD,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC1F,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,KAAK,uBAAY,oBAAoB,CAAC,CAAC;IACrD,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,SAAS,uBAAY,oBAAoB,CAAC,CAAC;IACzD,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,gDAAgD,EAAE,QAAQ,CAAC,CAAC,CAAC;IACnF,IAAI,YAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CACT,IAAA,iBAAS,EACP,0EAA0E,EAC1E,QAAQ,CACT,CACF,CAAC;QACF,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,KAAK,uBAAY,WAAW,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,SAAS,uBAAY,WAAW,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,kDAAkD,EAAE,QAAQ,CAAC,CAAC,CAAC;QACrF,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,UAAU,uBAAY,WAAW,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,cAAc,uBAAY,WAAW,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,OAAO,CAAC,GAAG,CACT,IAAI;QACF,IAAA,iBAAS,EACP,6FAA6F,EAC7F,MAAM,CACP,CACJ,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,IAAI;IACjB,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,cAAc,uBAAY,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;IAEjE,oBAAoB;IACpB,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,sCAAsC,EAAE,MAAM,CAAC,CAAC,CAAC;IACvE,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,EAAE,CAAC,CAAC;IACzC,OAAO,CAAC,GAAG,CAAC,wBAAwB,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,wBAAwB,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,sBAAsB,eAAe,EAAE,CAAC,CAAC;IAErD,4CAA4C;IAC5C,MAAM,0BAA0B,EAAE,CAAC;IAEnC,iDAAiD;IACjD,MAAM,aAAa,EAAE,CAAC;IAEtB,qDAAqD;IACrD,IAAI,eAAe,EAAE,CAAC;QACpB,MAAM,qBAAqB,EAAE,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,IAAA,iBAAS,EAAC,6BAA6B,EAAE,QAAQ,CAAC,CAAC,CAAC;QAChE,uBAAuB,EAAE,CAAC;IAC5B,CAAC;AACH,CAAC;AAED,+DAA+D;AAC/D,IAAI,WAAW,EAAE,CAAC;IAChB,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,OAAO,CAAC,KAAK,CACX,IAAA,iBAAS,EACP,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EACtF,KAAK,CACN,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}