// Generated by wxt
export {}
declare global {
  const ContentIndexer: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/content-indexer')['ContentIndexer']
  const ContentScriptContext: typeof import('wxt/utils/content-script-context')['ContentScriptContext']
  const EffectScope: typeof import('vue')['EffectScope']
  const InvalidMatchPattern: typeof import('wxt/utils/match-patterns')['InvalidMatchPattern']
  const MatchPattern: typeof import('wxt/utils/match-patterns')['MatchPattern']
  const ModelCacheManager: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/model-cache-manager')['ModelCacheManager']
  const OffscreenManager: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/offscreen-manager')['OffscreenManager']
  const PREDEFINED_MODELS: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['PREDEFINED_MODELS']
  const SIMDMathEngine: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/simd-math-engine')['SIMDMathEngine']
  const SemanticSimilarityEngine: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['SemanticSimilarityEngine']
  const SemanticSimilarityEngineProxy: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['SemanticSimilarityEngineProxy']
  const TextChunker: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/text-chunker')['TextChunker']
  const VectorDatabase: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['VectorDatabase']
  const browser: typeof import('wxt/browser')['browser']
  const canvasToDataURL: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['canvasToDataURL']
  const cleanupModelCache: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['cleanupModelCache']
  const clearAllVectorData: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['clearAllVectorData']
  const clearModelCache: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['clearModelCache']
  const compareModels: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['compareModels']
  const compressImage: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['compressImage']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createIframeUi: typeof import('wxt/utils/content-script-ui/iframe')['createIframeUi']
  const createImageBitmapFromUrl: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['createImageBitmapFromUrl']
  const createIntegratedUi: typeof import('wxt/utils/content-script-ui/integrated')['createIntegratedUi']
  const createShadowRootUi: typeof import('wxt/utils/content-script-ui/shadow-root')['createShadowRootUi']
  const cropAndResizeImage: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['cropAndResizeImage']
  const customRef: typeof import('vue')['customRef']
  const defineAppConfig: typeof import('wxt/utils/define-app-config')['defineAppConfig']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineBackground: typeof import('wxt/utils/define-background')['defineBackground']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineContentScript: typeof import('wxt/utils/define-content-script')['defineContentScript']
  const defineUnlistedScript: typeof import('wxt/utils/define-unlisted-script')['defineUnlistedScript']
  const defineWxtPlugin: typeof import('wxt/utils/define-wxt-plugin')['defineWxtPlugin']
  const effectScope: typeof import('vue')['effectScope']
  const fakeBrowser: typeof import('wxt/testing')['fakeBrowser']
  const getAllModelSizes: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getAllModelSizes']
  const getCacheStats: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getCacheStats']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getGlobalContentIndexer: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/content-indexer')['getGlobalContentIndexer']
  const getGlobalVectorDatabase: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['getGlobalVectorDatabase']
  const getGlobalVectorDatabaseSync: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['getGlobalVectorDatabaseSync']
  const getMessage: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/i18n')['getMessage']
  const getModelIdentifierWithVersion: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getModelIdentifierWithVersion']
  const getModelInfo: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getModelInfo']
  const getModelSizeInfo: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getModelSizeInfo']
  const getOnnxFileNameForVersion: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getOnnxFileNameForVersion']
  const h: typeof import('vue')['h']
  const hasAnyModelCache: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['hasAnyModelCache']
  const inject: typeof import('vue')['inject']
  const injectScript: typeof import('wxt/utils/inject-script')['injectScript']
  const isDefaultModelCached: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['isDefaultModelCached']
  const isI18nAvailable: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/i18n')['isI18nAvailable']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const listAvailableModels: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['listAvailableModels']
  const lruCache: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/lru-cache')['default']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const offscreenManager: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/offscreen-manager')['offscreenManager']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const recommendModelForDevice: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['recommendModelForDevice']
  const recommendModelForLanguage: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['recommendModelForLanguage']
  const ref: typeof import('vue')['ref']
  const resetGlobalVectorDatabase: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['resetGlobalVectorDatabase']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const stitchImages: typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['stitchImages']
  const storage: typeof import('wxt/utils/storage')['storage']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAppConfig: typeof import('wxt/utils/app-config')['useAppConfig']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useId: typeof import('vue')['useId']
  const useModel: typeof import('vue')['useModel']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Browser } from 'wxt/browser'
  import('wxt/browser')
  // @ts-ignore
  export type { StorageArea, WxtStorage, WxtStorageItem, StorageItemKey, StorageAreaChanges, MigrationError } from 'wxt/utils/storage'
  import('wxt/utils/storage')
  // @ts-ignore
  export type { WxtWindowEventMap } from 'wxt/utils/content-script-context'
  import('wxt/utils/content-script-context')
  // @ts-ignore
  export type { IframeContentScriptUi, IframeContentScriptUiOptions } from 'wxt/utils/content-script-ui/iframe'
  import('wxt/utils/content-script-ui/iframe')
  // @ts-ignore
  export type { IntegratedContentScriptUi, IntegratedContentScriptUiOptions } from 'wxt/utils/content-script-ui/integrated'
  import('wxt/utils/content-script-ui/integrated')
  // @ts-ignore
  export type { ShadowRootContentScriptUi, ShadowRootContentScriptUiOptions } from 'wxt/utils/content-script-ui/shadow-root'
  import('wxt/utils/content-script-ui/shadow-root')
  // @ts-ignore
  export type { ContentScriptUi, ContentScriptUiOptions, ContentScriptOverlayAlignment, ContentScriptAppendMode, ContentScriptInlinePositioningOptions, ContentScriptOverlayPositioningOptions, ContentScriptModalPositioningOptions, ContentScriptPositioningOptions, ContentScriptAnchoredOptions, AutoMountOptions, StopAutoMount, AutoMount } from 'wxt/utils/content-script-ui/types'
  import('wxt/utils/content-script-ui/types')
  // @ts-ignore
  export type { WxtAppConfig } from 'wxt/utils/define-app-config'
  import('wxt/utils/define-app-config')
  // @ts-ignore
  export type { ScriptPublicPath, InjectScriptOptions } from 'wxt/utils/inject-script'
  import('wxt/utils/inject-script')
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { ContentIndexer, IndexingOptions } from 'F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/content-indexer'
  import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/content-indexer')
  // @ts-ignore
  export type { ModelCacheManager, CacheMetadata, CacheEntry, CacheStats } from 'F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/model-cache-manager'
  import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/model-cache-manager')
  // @ts-ignore
  export type { OffscreenManager } from 'F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/offscreen-manager'
  import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/offscreen-manager')
  // @ts-ignore
  export type { SemanticSimilarityEngineProxy, SemanticSimilarityEngine, ModelPreset } from 'F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine'
  import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')
  // @ts-ignore
  export type { SIMDMathEngine } from 'F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/simd-math-engine'
  import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/simd-math-engine')
  // @ts-ignore
  export type { TextChunker, TextChunk, ChunkingOptions } from 'F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/text-chunker'
  import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/text-chunker')
  // @ts-ignore
  export type { VectorDatabase, VectorDocument, SearchResult, VectorDatabaseConfig } from 'F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database'
  import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly ContentIndexer: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/content-indexer')['ContentIndexer']>
    readonly ContentScriptContext: UnwrapRef<typeof import('wxt/utils/content-script-context')['ContentScriptContext']>
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly InvalidMatchPattern: UnwrapRef<typeof import('wxt/utils/match-patterns')['InvalidMatchPattern']>
    readonly MatchPattern: UnwrapRef<typeof import('wxt/utils/match-patterns')['MatchPattern']>
    readonly ModelCacheManager: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/model-cache-manager')['ModelCacheManager']>
    readonly OffscreenManager: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/offscreen-manager')['OffscreenManager']>
    readonly PREDEFINED_MODELS: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['PREDEFINED_MODELS']>
    readonly SIMDMathEngine: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/simd-math-engine')['SIMDMathEngine']>
    readonly SemanticSimilarityEngine: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['SemanticSimilarityEngine']>
    readonly SemanticSimilarityEngineProxy: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['SemanticSimilarityEngineProxy']>
    readonly TextChunker: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/text-chunker')['TextChunker']>
    readonly VectorDatabase: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['VectorDatabase']>
    readonly browser: UnwrapRef<typeof import('wxt/browser')['browser']>
    readonly canvasToDataURL: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['canvasToDataURL']>
    readonly cleanupModelCache: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['cleanupModelCache']>
    readonly clearAllVectorData: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['clearAllVectorData']>
    readonly clearModelCache: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['clearModelCache']>
    readonly compareModels: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['compareModels']>
    readonly compressImage: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['compressImage']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly createIframeUi: UnwrapRef<typeof import('wxt/utils/content-script-ui/iframe')['createIframeUi']>
    readonly createImageBitmapFromUrl: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['createImageBitmapFromUrl']>
    readonly createIntegratedUi: UnwrapRef<typeof import('wxt/utils/content-script-ui/integrated')['createIntegratedUi']>
    readonly createShadowRootUi: UnwrapRef<typeof import('wxt/utils/content-script-ui/shadow-root')['createShadowRootUi']>
    readonly cropAndResizeImage: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['cropAndResizeImage']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly defineAppConfig: UnwrapRef<typeof import('wxt/utils/define-app-config')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineBackground: UnwrapRef<typeof import('wxt/utils/define-background')['defineBackground']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineContentScript: UnwrapRef<typeof import('wxt/utils/define-content-script')['defineContentScript']>
    readonly defineUnlistedScript: UnwrapRef<typeof import('wxt/utils/define-unlisted-script')['defineUnlistedScript']>
    readonly defineWxtPlugin: UnwrapRef<typeof import('wxt/utils/define-wxt-plugin')['defineWxtPlugin']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly fakeBrowser: UnwrapRef<typeof import('wxt/testing')['fakeBrowser']>
    readonly getAllModelSizes: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getAllModelSizes']>
    readonly getCacheStats: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getCacheStats']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getGlobalContentIndexer: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/content-indexer')['getGlobalContentIndexer']>
    readonly getGlobalVectorDatabase: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['getGlobalVectorDatabase']>
    readonly getGlobalVectorDatabaseSync: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['getGlobalVectorDatabaseSync']>
    readonly getMessage: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/i18n')['getMessage']>
    readonly getModelIdentifierWithVersion: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getModelIdentifierWithVersion']>
    readonly getModelInfo: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getModelInfo']>
    readonly getModelSizeInfo: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getModelSizeInfo']>
    readonly getOnnxFileNameForVersion: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['getOnnxFileNameForVersion']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly hasAnyModelCache: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['hasAnyModelCache']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly injectScript: UnwrapRef<typeof import('wxt/utils/inject-script')['injectScript']>
    readonly isDefaultModelCached: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['isDefaultModelCached']>
    readonly isI18nAvailable: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/i18n')['isI18nAvailable']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly listAvailableModels: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['listAvailableModels']>
    readonly lruCache: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/lru-cache')['default']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly offscreenManager: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/offscreen-manager')['offscreenManager']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly onWatcherCleanup: UnwrapRef<typeof import('vue')['onWatcherCleanup']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly recommendModelForDevice: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['recommendModelForDevice']>
    readonly recommendModelForLanguage: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/semantic-similarity-engine')['recommendModelForLanguage']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly resetGlobalVectorDatabase: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/vector-database')['resetGlobalVectorDatabase']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly stitchImages: UnwrapRef<typeof import('F:/zuomianwenjian/gugemcp/mcp-chrome-master/app/chrome-extension/utils/image-utils')['stitchImages']>
    readonly storage: UnwrapRef<typeof import('wxt/utils/storage')['storage']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly useAppConfig: UnwrapRef<typeof import('wxt/utils/app-config')['useAppConfig']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}
