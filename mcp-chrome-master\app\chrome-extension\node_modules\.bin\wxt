#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_f247dc452d4261e186fdea6bb2b71618/node_modules/wxt/bin/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_f247dc452d4261e186fdea6bb2b71618/node_modules/wxt/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_f247dc452d4261e186fdea6bb2b71618/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_f247dc452d4261e186fdea6bb2b71618/node_modules/wxt/bin/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_f247dc452d4261e186fdea6bb2b71618/node_modules/wxt/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/wxt@0.20.7_@types+node@22.1_f247dc452d4261e186fdea6bb2b71618/node_modules:/proc/cygdrive/f/zuomianwenjian/gugemcp/mcp-chrome-master/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../wxt/bin/wxt.mjs" "$@"
else
  exec node  "$basedir/../wxt/bin/wxt.mjs" "$@"
fi
