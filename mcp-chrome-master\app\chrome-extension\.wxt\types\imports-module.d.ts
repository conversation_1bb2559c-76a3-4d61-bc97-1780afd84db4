// Generated by wxt
// Types for the #import virtual module
declare module '#imports' {
  export { browser, Browser } from 'wxt/browser';
  export { storage, StorageArea, WxtStorage, WxtStorageItem, StorageItemKey, StorageAreaChanges, MigrationError } from 'wxt/utils/storage';
  export { useAppConfig } from 'wxt/utils/app-config';
  export { ContentScriptContext, WxtWindowEventMap } from 'wxt/utils/content-script-context';
  export { createIframeUi, IframeContentScriptUi, IframeContentScriptUiOptions } from 'wxt/utils/content-script-ui/iframe';
  export { createIntegratedUi, IntegratedContentScriptUi, IntegratedContentScriptUiOptions } from 'wxt/utils/content-script-ui/integrated';
  export { createShadowRootUi, ShadowRootContentScriptUi, ShadowRootContentScriptUiOptions } from 'wxt/utils/content-script-ui/shadow-root';
  export { ContentScriptUi, ContentScriptUiOptions, ContentScriptOverlayAlignment, ContentScriptAppendMode, ContentScriptInlinePositioningOptions, ContentScriptOverlayPositioningOptions, ContentScriptModalPositioningOptions, ContentScriptPositioningOptions, ContentScriptAnchoredOptions, AutoMountOptions, StopAutoMount, AutoMount } from 'wxt/utils/content-script-ui/types';
  export { defineAppConfig, WxtAppConfig } from 'wxt/utils/define-app-config';
  export { defineBackground } from 'wxt/utils/define-background';
  export { defineContentScript } from 'wxt/utils/define-content-script';
  export { defineUnlistedScript } from 'wxt/utils/define-unlisted-script';
  export { defineWxtPlugin } from 'wxt/utils/define-wxt-plugin';
  export { injectScript, ScriptPublicPath, InjectScriptOptions } from 'wxt/utils/inject-script';
  export { InvalidMatchPattern, MatchPattern } from 'wxt/utils/match-patterns';
  export { onActivated, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onErrorCaptured, onDeactivated, onMounted, onServerPrefetch, onUnmounted, onUpdated, useAttrs, useSlots, computed, customRef, isReadonly, isRef, isProxy, isReactive, markRaw, reactive, readonly, ref, shallowReactive, shallowReadonly, shallowRef, triggerRef, toRaw, toRef, toRefs, toValue, unref, watch, watchEffect, watchPostEffect, watchSyncEffect, defineComponent, defineAsyncComponent, getCurrentInstance, h, inject, nextTick, provide, useCssModule, createApp, effectScope, EffectScope, getCurrentScope, onScopeDispose, Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef, onRenderTracked, onRenderTriggered, resolveComponent, useCssVars, useModel, onWatcherCleanup, useId, useTemplateRef } from 'vue';
  export { fakeBrowser } from 'wxt/testing';
  export { ContentIndexer, ContentIndexer, getGlobalContentIndexer, IndexingOptions } from '../utils/content-indexer';
  export { getMessage, isI18nAvailable } from '../utils/i18n';
  export { createImageBitmapFromUrl, stitchImages, cropAndResizeImage, canvasToDataURL, compressImage } from '../utils/image-utils';
  export { default as lruCache } from '../utils/lru-cache';
  export { ModelCacheManager, ModelCacheManager, CacheMetadata, CacheEntry, CacheStats } from '../utils/model-cache-manager';
  export { OffscreenManager, OffscreenManager, offscreenManager } from '../utils/offscreen-manager';
  export { clearModelCache, getCacheStats, cleanupModelCache, isDefaultModelCached, hasAnyModelCache, PREDEFINED_MODELS, getModelInfo, listAvailableModels, recommendModelForLanguage, recommendModelForDevice, getModelSizeInfo, compareModels, getOnnxFileNameForVersion, getModelIdentifierWithVersion, getAllModelSizes, SemanticSimilarityEngineProxy, SemanticSimilarityEngineProxy, SemanticSimilarityEngine, SemanticSimilarityEngine, ModelPreset } from '../utils/semantic-similarity-engine';
  export { SIMDMathEngine, SIMDMathEngine } from '../utils/simd-math-engine';
  export { TextChunker, TextChunker, TextChunk, ChunkingOptions } from '../utils/text-chunker';
  export { VectorDatabase, VectorDatabase, getGlobalVectorDatabase, getGlobalVectorDatabaseSync, resetGlobalVectorDatabase, clearAllVectorData, VectorDocument, SearchResult, VectorDatabaseConfig } from '../utils/vector-database';
}
