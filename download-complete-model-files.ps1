# Download complete model files (including tokenizer etc.)

Write-Host "Downloading complete model files" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

$modelsDir = "chrome-extension\models\Xenova"

# Files needed for multilingual-e5-small
$smallModelFiles = @{
    "tokenizer.json" = "https://huggingface.co/Xenova/multilingual-e5-small/resolve/main/tokenizer.json"
    "tokenizer_config.json" = "https://huggingface.co/Xenova/multilingual-e5-small/resolve/main/tokenizer_config.json"
    "config.json" = "https://huggingface.co/Xenova/multilingual-e5-small/resolve/main/config.json"
    "special_tokens_map.json" = "https://huggingface.co/Xenova/multilingual-e5-small/resolve/main/special_tokens_map.json"
    "vocab.txt" = "https://huggingface.co/Xenova/multilingual-e5-small/resolve/main/vocab.txt"
}

# Files needed for multilingual-e5-base
$baseModelFiles = @{
    "tokenizer.json" = "https://huggingface.co/Xenova/multilingual-e5-base/resolve/main/tokenizer.json"
    "tokenizer_config.json" = "https://huggingface.co/Xenova/multilingual-e5-base/resolve/main/tokenizer_config.json"
    "config.json" = "https://huggingface.co/Xenova/multilingual-e5-base/resolve/main/config.json"
    "special_tokens_map.json" = "https://huggingface.co/Xenova/multilingual-e5-base/resolve/main/special_tokens_map.json"
    "vocab.txt" = "https://huggingface.co/Xenova/multilingual-e5-base/resolve/main/vocab.txt"
}

# Download multilingual-e5-small files
Write-Host "Downloading multilingual-e5-small config files..." -ForegroundColor Yellow
$smallDir = "$modelsDir\multilingual-e5-small"

foreach ($file in $smallModelFiles.GetEnumerator()) {
    $fileName = $file.Key
    $url = $file.Value
    $filePath = "$smallDir\$fileName"
    
    if (-not (Test-Path $filePath)) {
        Write-Host "Downloading: $fileName" -ForegroundColor Cyan
        try {
            Invoke-WebRequest -Uri $url -OutFile $filePath -UseBasicParsing
            Write-Host "Success: $fileName downloaded" -ForegroundColor Green
        } catch {
            Write-Host "Failed: $fileName - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "Exists: $fileName" -ForegroundColor Green
    }
}

Write-Host ""

# Download multilingual-e5-base files
Write-Host "Downloading multilingual-e5-base config files..." -ForegroundColor Yellow
$baseDir = "$modelsDir\multilingual-e5-base"

foreach ($file in $baseModelFiles.GetEnumerator()) {
    $fileName = $file.Key
    $url = $file.Value
    $filePath = "$baseDir\$fileName"
    
    if (-not (Test-Path $filePath)) {
        Write-Host "Downloading: $fileName" -ForegroundColor Cyan
        try {
            Invoke-WebRequest -Uri $url -OutFile $filePath -UseBasicParsing
            Write-Host "Success: $fileName downloaded" -ForegroundColor Green
        } catch {
            Write-Host "Failed: $fileName - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "Exists: $fileName" -ForegroundColor Green
    }
}

Write-Host ""

# Check final results
Write-Host "Checking file integrity..." -ForegroundColor Yellow

$requiredFiles = @("tokenizer.json", "tokenizer_config.json", "config.json", "special_tokens_map.json", "vocab.txt", "model_quantized.onnx")

Write-Host "multilingual-e5-small:" -ForegroundColor Cyan
foreach ($file in $requiredFiles) {
    $path = "$smallDir\$file"
    if (Test-Path $path) {
        $size = [math]::Round((Get-Item $path).Length / 1KB, 2)
        Write-Host "  OK: $file ($size KB)" -ForegroundColor Green
    } else {
        Write-Host "  MISSING: $file" -ForegroundColor Red
    }
}

Write-Host "multilingual-e5-base:" -ForegroundColor Cyan
foreach ($file in $requiredFiles) {
    $path = "$baseDir\$file"
    if (Test-Path $path) {
        $size = [math]::Round((Get-Item $path).Length / 1KB, 2)
        Write-Host "  OK: $file ($size KB)" -ForegroundColor Green
    } else {
        Write-Host "  MISSING: $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Download complete! Now reload Chrome extension and test." -ForegroundColor Green

Read-Host "Press Enter to exit..."
