# Chrome MCP Server API 文档

## 📋 概述

Chrome MCP Server 提供了丰富的API接口，支持通过MCP协议控制Chrome浏览器的各种功能。

## 🛠️ MCP工具列表

### 1. 标签页管理

#### `get_tabs`
获取所有打开的标签页信息

**参数**: 无

**返回值**:
```json
{
  "tabs": [
    {
      "id": 123,
      "url": "https://example.com",
      "title": "Example Page",
      "active": true,
      "windowId": 1
    }
  ]
}
```

#### `create_tab`
创建新标签页

**参数**:
```json
{
  "url": "https://example.com",
  "active": true
}
```

#### `close_tab`
关闭指定标签页

**参数**:
```json
{
  "tabId": 123
}
```

#### `switch_tab`
切换到指定标签页

**参数**:
```json
{
  "tabId": 123
}
```

### 2. 页面导航

#### `navigate_to`
导航到指定URL

**参数**:
```json
{
  "url": "https://example.com",
  "tabId": 123
}
```

#### `go_back`
后退到上一页

**参数**:
```json
{
  "tabId": 123
}
```

#### `go_forward`
前进到下一页

**参数**:
```json
{
  "tabId": 123
}
```

#### `reload_page`
刷新页面

**参数**:
```json
{
  "tabId": 123,
  "bypassCache": false
}
```

### 3. 页面内容操作

#### `get_page_content`
获取页面HTML内容

**参数**:
```json
{
  "tabId": 123,
  "format": "html" // 或 "text"
}
```

**返回值**:
```json
{
  "content": "<html>...</html>",
  "title": "Page Title",
  "url": "https://example.com"
}
```

#### `click_element`
点击页面元素

**参数**:
```json
{
  "tabId": 123,
  "selector": "#button-id",
  "coordinates": {"x": 100, "y": 200}
}
```

#### `type_text`
在元素中输入文本

**参数**:
```json
{
  "tabId": 123,
  "selector": "#input-field",
  "text": "Hello World",
  "clear": true
}
```

#### `scroll_page`
滚动页面

**参数**:
```json
{
  "tabId": 123,
  "direction": "down", // up, down, left, right
  "amount": 500
}
```

### 4. 截图和视觉

#### `take_screenshot`
截取页面截图

**参数**:
```json
{
  "tabId": 123,
  "format": "png", // png, jpeg
  "quality": 90,
  "fullPage": false
}
```

**返回值**:
```json
{
  "screenshot": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "width": 1920,
  "height": 1080
}
```

#### `highlight_element`
高亮显示页面元素

**参数**:
```json
{
  "tabId": 123,
  "selector": ".important-element",
  "color": "#ff0000",
  "duration": 3000
}
```

### 5. 表单操作

#### `fill_form`
填写表单

**参数**:
```json
{
  "tabId": 123,
  "formData": {
    "#username": "<EMAIL>",
    "#password": "password123",
    "#remember": true
  }
}
```

#### `submit_form`
提交表单

**参数**:
```json
{
  "tabId": 123,
  "formSelector": "#login-form"
}
```

### 6. 语义搜索

#### `semantic_search`
在标签页内容中进行语义搜索

**参数**:
```json
{
  "query": "人工智能相关内容",
  "tabIds": [123, 456],
  "limit": 10,
  "threshold": 0.7
}
```

**返回值**:
```json
{
  "results": [
    {
      "tabId": 123,
      "title": "AI技术文章",
      "url": "https://example.com/ai",
      "score": 0.95,
      "snippet": "人工智能技术的发展..."
    }
  ]
}
```

#### `index_page_content`
为页面内容建立语义索引

**参数**:
```json
{
  "tabId": 123,
  "force": false
}
```

### 7. 书签管理

#### `get_bookmarks`
获取书签列表

**参数**:
```json
{
  "query": "技术",
  "limit": 50
}
```

#### `create_bookmark`
创建书签

**参数**:
```json
{
  "title": "技术文章",
  "url": "https://example.com",
  "parentId": "1"
}
```

#### `search_bookmarks`
搜索书签

**参数**:
```json
{
  "query": "JavaScript",
  "limit": 20
}
```

### 8. 历史记录

#### `get_history`
获取浏览历史

**参数**:
```json
{
  "query": "github",
  "startTime": 1640995200000,
  "endTime": 1641081600000,
  "maxResults": 100
}
```

#### `search_history`
搜索历史记录

**参数**:
```json
{
  "text": "机器学习",
  "maxResults": 50
}
```

### 9. 网络监控

#### `start_network_capture`
开始网络请求监控

**参数**:
```json
{
  "tabId": 123,
  "filters": {
    "resourceTypes": ["xmlhttprequest", "fetch"],
    "urls": ["*api*"]
  }
}
```

#### `stop_network_capture`
停止网络请求监控

**参数**:
```json
{
  "tabId": 123
}
```

#### `get_network_requests`
获取捕获的网络请求

**参数**:
```json
{
  "tabId": 123,
  "limit": 100
}
```

### 10. 脚本注入

#### `inject_script`
注入JavaScript代码

**参数**:
```json
{
  "tabId": 123,
  "code": "document.querySelector('.ad').style.display = 'none';",
  "world": "MAIN" // MAIN 或 ISOLATED
}
```

#### `inject_css`
注入CSS样式

**参数**:
```json
{
  "tabId": 123,
  "css": ".advertisement { display: none !important; }",
  "origin": "USER"
}
```

## 🔧 Native Messaging API

### 消息格式

所有Native Messaging消息都使用JSON格式：

```json
{
  "type": "message_type",
  "payload": {
    // 消息内容
  },
  "id": "unique_message_id"
}
```

### 消息类型

#### `start`
启动服务器

**请求**:
```json
{
  "type": "start",
  "payload": {
    "port": 12306
  }
}
```

**响应**:
```json
{
  "type": "started",
  "payload": {
    "port": 12306,
    "pid": 12345
  }
}
```

#### `stop`
停止服务器

**请求**:
```json
{
  "type": "stop",
  "payload": {}
}
```

#### `ping`
心跳检测

**请求**:
```json
{
  "type": "ping",
  "payload": {}
}
```

**响应**:
```json
{
  "type": "pong",
  "payload": {
    "timestamp": 1640995200000
  }
}
```

## 🌐 HTTP API

### 基础URL
```
http://127.0.0.1:12306
```

### 端点列表

#### `GET /health`
健康检查

**响应**:
```json
{
  "status": "ok",
  "timestamp": 1640995200000,
  "version": "1.0.0"
}
```

#### `POST /mcp`
MCP协议端点

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "get_tabs",
    "arguments": {}
  },
  "id": 1
}
```

#### `GET /status`
服务状态

**响应**:
```json
{
  "server": {
    "running": true,
    "port": 12306,
    "uptime": 3600
  },
  "extension": {
    "connected": true,
    "version": "1.0.0"
  },
  "semantic": {
    "initialized": true,
    "model": "multilingual-e5-small",
    "dimension": 384
  }
}
```

## 🔍 语义引擎API

### 初始化

```typescript
const engine = new SemanticSimilarityEngine({
  modelIdentifier: 'Xenova/multilingual-e5-small',
  useLocalFiles: true,
  dimension: 384
});

await engine.initialize();
```

### 文本向量化

```typescript
const vector = await engine.embedText('这是一段测试文本');
console.log(vector); // Float32Array(384)
```

### 相似度搜索

```typescript
const results = await engine.searchSimilar('人工智能', {
  limit: 10,
  threshold: 0.7
});
```

### 批量处理

```typescript
const texts = ['文本1', '文本2', '文本3'];
const vectors = await engine.embedBatch(texts);
```

## 📊 向量数据库API

### 添加文档

```typescript
await vectorDB.addDocument({
  content: '文档内容',
  metadata: {
    url: 'https://example.com',
    title: '文档标题',
    tabId: 123
  }
});
```

### 搜索文档

```typescript
const results = await vectorDB.search('搜索查询', {
  limit: 10,
  filter: {
    tabId: 123
  }
});
```

### 删除文档

```typescript
await vectorDB.removeDocument(documentId);
await vectorDB.removeTabDocuments(tabId);
```

## 🚨 错误处理

### 错误格式

```json
{
  "error": {
    "code": -32601,
    "message": "Method not found",
    "data": {
      "method": "unknown_method"
    }
  }
}
```

### 常见错误码

- `-32700`: 解析错误
- `-32600`: 无效请求
- `-32601`: 方法不存在
- `-32602`: 无效参数
- `-32603`: 内部错误
- `-32000`: 扩展未连接
- `-32001`: 标签页不存在
- `-32002`: 权限不足

## 📝 使用示例

### 基础浏览器控制

```javascript
// 打开新标签页
await callTool('create_tab', {
  url: 'https://github.com',
  active: true
});

// 截图
const screenshot = await callTool('take_screenshot', {
  tabId: 123,
  fullPage: true
});

// 搜索内容
const results = await callTool('semantic_search', {
  query: 'JavaScript教程',
  limit: 5
});
```

### 自动化任务

```javascript
// 自动填写表单
await callTool('fill_form', {
  tabId: 123,
  formData: {
    '#email': '<EMAIL>',
    '#message': '这是自动填写的消息'
  }
});

// 提交表单
await callTool('submit_form', {
  tabId: 123,
  formSelector: '#contact-form'
});
```

---

## 📚 更多资源

- [技术文档](./技术文档.md)
- [部署指南](./部署.md)
- [GitHub仓库](https://github.com/hangwin/mcp-chrome)
