# Chrome MCP Server 技术文档

## 📋 项目概述

Chrome MCP Server 是一个基于 Model Context Protocol (MCP) 的浏览器自动化系统，通过AI模型控制Chrome浏览器执行各种任务。

### 🎯 核心特性

- **本地语义搜索**：使用本地ONNX模型，无需网络依赖
- **Native Messaging**：Chrome扩展与本地服务的安全通信
- **多模态AI支持**：支持文本和视觉理解
- **实时向量索引**：基于HNSW算法的高效相似度搜索
- **跨标签页操作**：统一管理多个浏览器标签页

## 🏗️ 系统架构

```
┌─────────────────┐    MCP Protocol    ┌─────────────────┐
│   AI Model      │◄──────────────────►│  Native Server  │
│  (LM Studio)    │   HTTP/WebSocket   │   (Node.js)     │
└─────────────────┘                    └─────────────────┘
                                                │
                                                │ Native Messaging
                                                │ (stdio/JSON-RPC)
                                                ▼
┌─────────────────┐    Chrome APIs     ┌─────────────────┐
│  Chrome Browser │◄──────────────────►│ Chrome Extension│
│   (Target)      │                    │   (Controller)  │
└─────────────────┘                    └─────────────────┘
                                                │
                                                │ Offscreen API
                                                ▼
                                       ┌─────────────────┐
                                       │ Semantic Engine │
                                       │ (ONNX Runtime)  │
                                       └─────────────────┘
```

## 🔧 核心组件

### 1. Chrome Extension (前端控制器)

**位置**: `app/chrome-extension/`

**主要文件**:
- `entrypoints/background/` - Service Worker后台脚本
- `entrypoints/popup/` - 扩展弹窗界面
- `entrypoints/content/` - 内容脚本注入
- `entrypoints/offscreen/` - 离屏文档处理
- `utils/` - 工具类和核心逻辑

**关键功能**:
- 浏览器API封装和调用
- 语义搜索引擎管理
- Native Messaging通信
- 用户界面交互

### 2. Native Server (后端服务)

**位置**: `app/native-server/`

**主要文件**:
- `src/server/` - Fastify HTTP服务器
- `src/mcp/` - MCP协议实现
- `src/native-messaging-host.ts` - Native Messaging主机
- `src/cli.ts` - 命令行工具

**关键功能**:
- MCP协议服务器
- Chrome扩展通信桥接
- 工具调用路由和执行
- 进程生命周期管理

### 3. Semantic Engine (语义引擎)

**位置**: `app/chrome-extension/utils/semantic-similarity-engine.ts`

**核心技术**:
- **ONNX Runtime Web**: 浏览器中运行机器学习模型
- **Transformers.js**: 预训练模型加载和推理
- **Web Workers**: 后台处理避免UI阻塞

**支持模型**:
- `multilingual-e5-small` (118MB, 384维)
- `multilingual-e5-base` (279MB, 768维)

### 4. Vector Database (向量数据库)

**位置**: `app/chrome-extension/utils/vector-database.ts`

**核心技术**:
- **hnswlib-wasm**: 高性能近似最近邻搜索
- **IndexedDB**: 浏览器本地存储
- **Emscripten FS**: WASM文件系统

**功能特性**:
- 实时向量索引构建
- 跨标签页内容搜索
- 增量更新和持久化

## 📊 数据流程

### 1. 语义搜索流程

```
用户查询 → Tokenizer → Embedding Model → 向量化
    ↓
向量数据库 → HNSW搜索 → 相似度排序 → 结果返回
```

### 2. MCP工具调用流程

```
AI模型 → MCP请求 → Native Server → Chrome Extension → Browser API → 执行结果
```

### 3. 内容索引流程

```
页面加载 → 内容提取 → 文本分块 → 向量化 → 索引存储
```

## ⚙️ 配置详情

### 1. 本地模型配置

**配置文件**: `app/chrome-extension/utils/semantic-similarity-engine.ts`

```typescript
const config = {
  useLocalFiles: true,           // 强制使用本地文件
  modelIdentifier: 'Xenova/multilingual-e5-small',
  onnxModelFile: 'model_quantized.onnx',
  numThreads: 1,
  executionProviders: ['wasm'],
  maxLength: 256,
  cacheSize: 1000
};
```

### 2. Native Messaging配置

**配置文件**: `app/native-server/src/scripts/constant.ts`

```typescript
export const EXTENSION_ID = 'nflcmnkchaaogfjpcdnlgmleblhhmhdg';
export const HOST_NAME = 'com.chromemcp.nativehost';
export const DESCRIPTION = 'Node.js Host for Browser Bridge Extension';
```

**注册表位置**: `HKCU\Software\Google\Chrome\NativeMessagingHosts\com.chromemcp.nativehost`

### 3. 服务器配置

**配置文件**: `app/native-server/src/constant/index.ts`

```typescript
export const SERVER_CONFIG = {
  HOST: '127.0.0.1',
  CORS_ORIGIN: true,
  LOGGER_ENABLED: false,
};

export const NATIVE_SERVER_PORT = 56889;
```

## 🔍 关键算法

### 1. 文本向量化

```typescript
async function embedText(text: string): Promise<Float32Array> {
  const inputs = await this.tokenizer(text, {
    padding: true,
    truncation: true,
    max_length: this.config.maxLength,
    return_tensors: 'pt'
  });
  
  const outputs = await this.session.run(inputs);
  return outputs.last_hidden_state.mean(1); // 平均池化
}
```

### 2. 相似度搜索

```typescript
async function searchSimilar(queryVector: Float32Array, k: number = 10) {
  const results = this.index.searchKnn(queryVector, k);
  return results.map(result => ({
    id: result.label,
    score: result.distance,
    content: this.documentMappings.get(result.label)
  }));
}
```

### 3. 增量索引更新

```typescript
async function addDocument(content: string, metadata: any) {
  const vector = await this.embedText(content);
  const label = this.nextLabel++;
  
  this.index.addPoint(vector, label);
  this.documentMappings.set(label, { content, metadata });
  
  await this.persistChanges();
}
```

## 🛠️ 开发指南

### 1. 本地开发环境

```bash
# 安装依赖
pnpm install

# 开发模式启动
cd app/chrome-extension
pnpm run dev

cd app/native-server
pnpm run dev
```

### 2. 构建生产版本

```bash
# 构建所有组件
pnpm run build

# 或分别构建
pnpm --filter chrome-mcp-shared build
pnpm --filter chrome-mcp-server build
cd app/native-server && pnpm run build
```

### 3. 调试技巧

**Chrome扩展调试**:
- 打开 `chrome://extensions/`
- 点击扩展的"检查视图"
- 查看 Service Worker 和 Offscreen 日志

**Native Server调试**:
```bash
# 启用详细日志
DEBUG=* node dist/index.js
```

**语义引擎调试**:
- 在浏览器开发者工具中查看 Offscreen 页面
- 监控 ONNX Runtime 和 Transformers.js 日志

## 📈 性能优化

### 1. 内存管理

- 使用 Web Workers 避免主线程阻塞
- 实现向量缓存和LRU淘汰策略
- 定期清理未使用的模型资源

### 2. 存储优化

- 使用 IndexedDB 进行大数据存储
- 实现增量同步减少I/O开销
- 压缩向量数据减少存储空间

### 3. 计算优化

- 使用 WASM 加速向量计算
- 批量处理减少模型调用次数
- 异步处理避免界面卡顿

## 🔒 安全考虑

### 1. 权限控制

- 最小权限原则配置Chrome扩展权限
- Native Messaging白名单验证
- 用户数据本地存储，不上传云端

### 2. 数据保护

- 敏感信息加密存储
- 跨域请求严格验证
- 用户隐私数据匿名化处理

## 🚨 故障排除

### 1. 常见问题

**语义引擎初始化失败**:
- 检查模型文件完整性
- 验证ONNX Runtime兼容性
- 清理浏览器缓存重试

**Native Messaging连接失败**:
- 验证扩展ID配置正确
- 检查注册表项是否存在
- 确认Node.js路径正确

**向量搜索结果不准确**:
- 检查文本预处理逻辑
- 调整相似度阈值
- 重建向量索引

### 2. 日志分析

**关键日志位置**:
- Chrome扩展: DevTools → Extensions → Service Worker
- Native Server: 控制台输出
- 语义引擎: DevTools → Offscreen Document

**重要日志关键词**:
- `SemanticSimilarityEngine`: 语义引擎相关
- `VectorDatabase`: 向量数据库操作
- `NativeMessagingHost`: Native通信
- `FS.syncfs`: 文件系统同步（警告可忽略）

## 📚 扩展开发

### 1. 添加新的MCP工具

```typescript
// 在 native-server 中添加新工具
export const newTool: Tool = {
  name: "new_tool",
  description: "新工具描述",
  inputSchema: {
    type: "object",
    properties: {
      param: { type: "string", description: "参数描述" }
    }
  }
};
```

### 2. 扩展语义搜索功能

```typescript
// 添加新的向量化策略
class CustomEmbeddingStrategy {
  async embed(text: string): Promise<Float32Array> {
    // 自定义向量化逻辑
  }
}
```

### 3. 集成新的AI模型

```typescript
// 支持新的ONNX模型
const modelConfig = {
  modelIdentifier: 'custom/model-name',
  dimension: 512,
  onnxModelFile: 'custom_model.onnx'
};
```

## 🔄 版本管理和升级

### 1. 版本号规范

项目采用语义化版本控制 (SemVer):
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 2. 升级策略

**Chrome扩展升级**:
```bash
# 重新构建扩展
cd app/chrome-extension
pnpm run build

# 在Chrome中重新加载扩展
# chrome://extensions/ → 重新加载
```

**Native Server升级**:
```bash
# 停止现有服务
# 重新构建
cd app/native-server
pnpm run build

# 重新注册（如果配置有变化）
pnpm run register:dev
```

**模型升级**:
```bash
# 更新模型文件
# 重新运行配置脚本
powershell -ExecutionPolicy Bypass -File setup-local-models.ps1
```

### 3. 兼容性检查

**浏览器兼容性**:
- Chrome 88+ (支持Offscreen API)
- Edge 88+ (基于Chromium)

**Node.js兼容性**:
- Node.js 14.0.0+
- 推荐使用 LTS 版本

**操作系统兼容性**:
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 18.04+)

## 🧪 测试框架

### 1. 单元测试

**Chrome扩展测试**:
```bash
cd app/chrome-extension
pnpm run test
```

**Native Server测试**:
```bash
cd app/native-server
pnpm run test
```

### 2. 集成测试

**端到端测试流程**:
1. 启动Native Server
2. 加载Chrome扩展
3. 执行MCP工具调用
4. 验证结果正确性

**测试用例示例**:
```typescript
describe('Semantic Search', () => {
  test('should embed text correctly', async () => {
    const engine = new SemanticSimilarityEngine(config);
    await engine.initialize();

    const vector = await engine.embedText('test content');
    expect(vector).toBeInstanceOf(Float32Array);
    expect(vector.length).toBe(384); // for e5-small
  });
});
```

### 3. 性能测试

**基准测试指标**:
- 模型加载时间: < 5秒
- 文本向量化速度: > 100 tokens/秒
- 向量搜索延迟: < 100ms
- 内存使用: < 500MB

## 📊 监控和日志

### 1. 日志级别

```typescript
enum LogLevel {
  ERROR = 0,   // 错误信息
  WARN = 1,    // 警告信息
  INFO = 2,    // 一般信息
  DEBUG = 3    // 调试信息
}
```

### 2. 关键指标监控

**性能指标**:
- 模型推理时间
- 向量搜索响应时间
- 内存使用情况
- 错误率统计

**业务指标**:
- 工具调用次数
- 用户活跃度
- 功能使用分布

### 3. 错误追踪

**错误分类**:
- 模型加载错误
- 网络通信错误
- 权限访问错误
- 数据处理错误

**错误处理策略**:
```typescript
try {
  await riskyOperation();
} catch (error) {
  logger.error('Operation failed', {
    error: error.message,
    stack: error.stack,
    context: getCurrentContext()
  });

  // 降级处理
  await fallbackOperation();
}
```

## 🔧 配置管理

### 1. 环境配置

**开发环境** (`.env.development`):
```env
NODE_ENV=development
LOG_LEVEL=debug
ENABLE_CORS=true
MODEL_CACHE_SIZE=1000
```

**生产环境** (`.env.production`):
```env
NODE_ENV=production
LOG_LEVEL=info
ENABLE_CORS=false
MODEL_CACHE_SIZE=5000
```

### 2. 动态配置

**运行时配置更新**:
```typescript
// 支持热更新的配置项
interface DynamicConfig {
  similarityThreshold: number;
  maxSearchResults: number;
  cacheSize: number;
  logLevel: LogLevel;
}

// 配置更新接口
async function updateConfig(newConfig: Partial<DynamicConfig>) {
  await configManager.update(newConfig);
  await notifyComponents(newConfig);
}
```

### 3. 配置验证

```typescript
const configSchema = {
  type: 'object',
  properties: {
    modelIdentifier: { type: 'string' },
    dimension: { type: 'number', minimum: 1 },
    useLocalFiles: { type: 'boolean' }
  },
  required: ['modelIdentifier', 'dimension']
};

function validateConfig(config: any): boolean {
  return ajv.validate(configSchema, config);
}
```

## 🚀 部署自动化

### 1. 构建脚本

**完整构建流程** (`build.ps1`):
```powershell
# 清理旧构建
Remove-Item -Recurse -Force dist, .output -ErrorAction SilentlyContinue

# 安装依赖
pnpm install

# 构建共享包
pnpm --filter chrome-mcp-shared build

# 构建扩展
cd app/chrome-extension
pnpm run build

# 构建服务器
cd ../native-server
pnpm run build

# 打包发布
Compress-Archive -Path .output/chrome-mv3 -DestinationPath release.zip
```

### 2. 持续集成

**GitHub Actions配置** (`.github/workflows/build.yml`):
```yaml
name: Build and Test
on: [push, pull_request]

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install -g pnpm
      - run: pnpm install
      - run: pnpm run build
      - run: pnpm run test
```

### 3. 发布流程

**自动化发布**:
1. 版本号更新
2. 构建所有组件
3. 运行测试套件
4. 生成发布包
5. 创建GitHub Release
6. 更新文档

## 📈 性能分析

### 1. 内存分析

**内存使用分布**:
- ONNX模型: ~200MB
- 向量索引: ~50MB
- 文档缓存: ~100MB
- 其他组件: ~50MB

**内存优化策略**:
```typescript
// 实现LRU缓存
class LRUCache<K, V> {
  private cache = new Map<K, V>();
  private maxSize: number;

  constructor(maxSize: number) {
    this.maxSize = maxSize;
  }

  get(key: K): V | undefined {
    const value = this.cache.get(key);
    if (value !== undefined) {
      // 移到最前面
      this.cache.delete(key);
      this.cache.set(key, value);
    }
    return value;
  }

  set(key: K, value: V): void {
    if (this.cache.size >= this.maxSize) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}
```

### 2. CPU性能分析

**性能瓶颈识别**:
- 模型推理: 60-80% CPU使用
- 向量搜索: 10-20% CPU使用
- 数据序列化: 5-10% CPU使用

**优化措施**:
- 使用Web Workers并行处理
- 批量处理减少调用开销
- 缓存计算结果避免重复计算

### 3. I/O性能优化

**存储优化**:
```typescript
// 批量写入减少I/O次数
class BatchWriter {
  private buffer: Array<{key: string, value: any}> = [];
  private batchSize = 100;

  async write(key: string, value: any) {
    this.buffer.push({key, value});

    if (this.buffer.length >= this.batchSize) {
      await this.flush();
    }
  }

  async flush() {
    if (this.buffer.length === 0) return;

    await this.storage.batchWrite(this.buffer);
    this.buffer = [];
  }
}
```

---

## 📞 技术支持

如需技术支持或有疑问，请：
1. 查看项目文档和故障排除指南
2. 检查GitHub Issues中的已知问题
3. 提交详细的错误报告和日志信息

**项目地址**: https://github.com/hangwin/mcp-chrome

**技术栈总结**:
- 前端: TypeScript + Chrome Extension APIs + ONNX Runtime Web
- 后端: Node.js + Fastify + MCP SDK
- 存储: IndexedDB + WASM文件系统
- AI: Transformers.js + ONNX模型
- 通信: Native Messaging + JSON-RPC
